<div class="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
    @if($getRecord()->receipts->count() > 0)
        <div class="mb-3">
            <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100">
                {{ __('Receipts for Invoice :number', ['number' => $getRecord()->invoice_number]) }}
            </h4>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                        <th class="text-left py-2 px-3 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Receipt Number') }}
                        </th>
                        <th class="text-left py-2 px-3 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Date') }}
                        </th>
                        <th class="text-left py-2 px-3 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Amount') }}
                        </th>
                        <th class="text-left py-2 px-3 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Status') }}
                        </th>
                        <th class="text-right py-2 px-3 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($getRecord()->receipts as $receipt)
                        <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                            <td class="py-2 px-3">
                                <div class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ $receipt->receipt_number }}
                                </div>
                                @if($receipt->payment_provider_object_id)
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $receipt->payment_provider_object_id }}
                                    </div>
                                @endif
                            </td>
                            <td class="py-2 px-3 text-gray-700 dark:text-gray-300">
                                {{ $receipt->created_at->format(config('app.datetime_format')) }}
                            </td>
                            <td class="py-2 px-3 text-gray-700 dark:text-gray-300">
                                @if($receipt->amount_paid)
                                    {{ money($receipt->amount_paid, $getRecord()->currency->code) }}
                                @else
                                    {{ money(abs($getRecord()->amount), $getRecord()->currency->code) }}
                                @endif
                            </td>
                            <td class="py-2 px-3">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                                    @if($receipt->receipt_status === 'paid')
                                        bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                    @elseif($receipt->receipt_status === 'pending')
                                        bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
                                    @elseif($receipt->receipt_status === 'failed')
                                        bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                                    @else
                                        bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
                                    @endif
                                ">
                                    {{ ucfirst($receipt->receipt_status ?? 'unknown') }}
                                </span>
                            </td>
                            <td class="py-2 px-3 text-right">
                                <button
                                    type="button"
                                    onclick="openReceiptModal('{{ $receipt->uuid }}', '{{ $receipt->receipt_number }}', '{{ $getRecord()->user->email }}', '{{ $getRecord()->user->name }}', '{{ $getRecord()->user->phone ?? '' }}', '{{ $getRecord()->user->address_line_1 ?? '' }}', '{{ $getRecord()->user->address_line_2 ?? '' }}', '{{ $getRecord()->user->city ?? '' }}', '{{ $getRecord()->user->postal_code ?? '' }}')"
                                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 rounded transition-colors">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    {{ __('View Receipt') }}
                                </button>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @else
        <div class="text-center py-6">
            <svg class="w-8 h-8 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                {{ __('No receipts found') }}
            </h3>
            <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ __('This transaction has no associated receipts.') }}
            </p>
        </div>
    @endif
</div>

<!-- Receipt Modal -->
<div id="receiptModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeReceiptModal()"></div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
            <form id="receiptForm" onsubmit="generateReceipt(event)">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                                {{ __('Customize Receipt') }}
                            </h3>
                            <div class="mt-4">
                                <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                    {{ __('Customize the receipt details before generating') }}
                                </p>

                                <div class="space-y-4">
                                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                        <div>
                                            <label for="receipt_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                {{ __('Email') }}
                                            </label>
                                            <input type="email" id="receipt_email" name="email" required
                                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </div>

                                        <div>
                                            <label for="receipt_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                {{ __('Phone') }}
                                            </label>
                                            <input type="tel" id="receipt_phone" name="phone"
                                                class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        </div>
                                    </div>

                                    <div>
                                        <label for="receipt_displayed_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            {{ __('Displayed Name') }}
                                        </label>
                                        <input type="text" id="receipt_displayed_name" name="displayed_name" required
                                            class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                    </div>

                                    <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-3">{{ __('Address') }}</h4>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <div>
                                                <label for="receipt_address_line_1" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    {{ __('Address Line 1') }}
                                                </label>
                                                <input type="text" id="receipt_address_line_1" name="address_line_1"
                                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>

                                            <div>
                                                <label for="receipt_address_line_2" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    {{ __('Address Line 2') }}
                                                </label>
                                                <input type="text" id="receipt_address_line_2" name="address_line_2"
                                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>

                                            <div>
                                                <label for="receipt_city" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    {{ __('City') }}
                                                </label>
                                                <input type="text" id="receipt_city" name="city"
                                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>

                                            <div>
                                                <label for="receipt_postal_code" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    {{ __('Postal Code') }}
                                                </label>
                                                <input type="text" id="receipt_postal_code" name="postal_code"
                                                    class="mt-1 block w-full border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-100 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        {{ __('Generate Receipt') }}
                    </button>
                    <button type="button" onclick="closeReceiptModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        {{ __('Cancel') }}
                    </button>
                </div>

                <input type="hidden" id="receipt_uuid" name="uuid">
            </form>
        </div>
    </div>
</div>

<script>
let currentReceiptUuid = null;

function openReceiptModal(uuid, receiptNumber, email, name, phone, addressLine1, addressLine2, city, postalCode) {
    currentReceiptUuid = uuid;

    // Populate form fields
    document.getElementById('receipt_email').value = email || '';
    document.getElementById('receipt_phone').value = phone || '';
    document.getElementById('receipt_displayed_name').value = name || '';
    document.getElementById('receipt_address_line_1').value = addressLine1 || '';
    document.getElementById('receipt_address_line_2').value = addressLine2 || '';
    document.getElementById('receipt_city').value = city || '';
    document.getElementById('receipt_postal_code').value = postalCode || '';
    document.getElementById('receipt_uuid').value = uuid;

    // Update modal title
    document.getElementById('modal-title').textContent = '{{ __("Customize Receipt") }} - ' + receiptNumber;

    // Show modal
    document.getElementById('receiptModal').classList.remove('hidden');
}

function closeReceiptModal() {
    document.getElementById('receiptModal').classList.add('hidden');
    currentReceiptUuid = null;
}

function generateReceipt(event) {
    event.preventDefault();

    if (!currentReceiptUuid) {
        return;
    }

    // TODO: Integration will be added later
    // For now, just redirect to the original route
    const url = '{{ route("invoice_receipt.generate", ["uuid" => ":uuid", "docType" => "receipt"]) }}'.replace(':uuid', currentReceiptUuid);
    window.open(url, '_blank');

    closeReceiptModal();
}

// Close modal when pressing Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape' && !document.getElementById('receiptModal').classList.contains('hidden')) {
        closeReceiptModal();
    }
});
</script>
