@php
    $record = $getRecord();
    $receiptsCount = $record->receipts()->count();
@endphp

@if($receiptsCount > 0)
    <div x-data="{ expanded: false }" class="w-full">
        <!-- Toggle Button -->
        <button @click="expanded = !expanded" 
                class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 rounded transition-colors">
            <svg x-show="!expanded" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <svg x-show="expanded" class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
            {{ __('Receipts') }} ({{ $receiptsCount }})
        </button>
        
        <!-- Collapsible Content -->
        <div x-show="expanded"
             x-transition:enter="transition ease-out duration-200"
             x-transition:enter-start="opacity-0 transform scale-95"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-150"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-95"
             class="mt-2 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
            
            <div class="mb-2">
                <h4 class="text-xs font-semibold text-gray-900 dark:text-gray-100">
                    {{ __('Receipts for Invoice :number', ['number' => $record->invoice_number]) }}
                </h4>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full text-xs">
                    <thead>
                        <tr class="border-b border-gray-200 dark:border-gray-700">
                            <th class="text-left py-1 px-2 font-semibold text-gray-900 dark:text-gray-100">
                                {{ __('Receipt Number') }}
                            </th>
                            <th class="text-left py-1 px-2 font-semibold text-gray-900 dark:text-gray-100">
                                {{ __('Date') }}
                            </th>
                            <th class="text-left py-1 px-2 font-semibold text-gray-900 dark:text-gray-100">
                                {{ __('Amount') }}
                            </th>
                            <th class="text-left py-1 px-2 font-semibold text-gray-900 dark:text-gray-100">
                                {{ __('Status') }}
                            </th>
                            <th class="text-right py-1 px-2 font-semibold text-gray-900 dark:text-gray-100">
                                {{ __('Actions') }}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($record->receipts as $receipt)
                            <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                                <td class="py-1 px-2">
                                    <div class="font-medium text-gray-900 dark:text-gray-100">
                                        {{ $receipt->receipt_number }}
                                    </div>
                                    @if($receipt->payment_provider_object_id)
                                        <div class="text-xs text-gray-500 dark:text-gray-400">
                                            {{ $receipt->payment_provider_object_id }}
                                        </div>
                                    @endif
                                </td>
                                <td class="py-1 px-2 text-gray-700 dark:text-gray-300">
                                    {{ $receipt->created_at->format(config('app.datetime_format')) }}
                                </td>
                                <td class="py-1 px-2 text-gray-700 dark:text-gray-300">
                                    @if($receipt->amount_paid)
                                        {{ money($receipt->amount_paid, $record->currency->code) }}
                                    @else
                                        {{ money(abs($record->amount), $record->currency->code) }}
                                    @endif
                                </td>
                                <td class="py-1 px-2">
                                    <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium
                                        @if($receipt->receipt_status === 'paid')
                                            bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                        @elseif($receipt->receipt_status === 'pending')
                                            bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
                                        @elseif($receipt->receipt_status === 'failed')
                                            bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                                        @else
                                            bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
                                        @endif
                                    ">
                                        {{ ucfirst($receipt->receipt_status ?? 'unknown') }}
                                    </span>
                                </td>
                                <td class="py-1 px-2 text-right">
                                    <a href="{{ route('invoice_receipt.generate', ['uuid' => $receipt->uuid, 'docType' => 'receipt']) }}"
                                       target="_blank"
                                       class="inline-flex items-center px-1.5 py-0.5 text-xs font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 rounded transition-colors">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        {{ __('Download') }}
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@else
    <span class="text-xs text-gray-500 dark:text-gray-400">{{ __('No receipts') }}</span>
@endif
